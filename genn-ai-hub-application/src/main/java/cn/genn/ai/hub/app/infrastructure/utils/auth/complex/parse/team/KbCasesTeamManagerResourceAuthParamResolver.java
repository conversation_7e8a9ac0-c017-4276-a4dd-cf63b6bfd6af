package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.IdListCommand;
import cn.genn.ai.hub.app.application.command.KbRepoCasesCommand;
import cn.genn.ai.hub.app.application.command.KbRepoQaPairEditCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.CasesRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbCasesTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver {

    private final KbRepoBaseInfoMapper kbRepoBaseInfoMapper;
    private final CasesRepositoryImpl casesRepository;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long kbId = switch (uniqueId) {
            case "editCases" -> {
                Long qaId = ((KbRepoCasesCommand) interceptor.getParamsMap().get("command")).getId();
                yield casesRepository.selectById(qaId).getRepoId();
            }
            case "deleteCases" -> {
                Long qaId = ((IdCommand) interceptor.getParamsMap().get("command")).getId();
                yield casesRepository.selectById(qaId).getRepoId();
            }
            case "batchDeleteCases" -> {
                Long qaId = ((IdListCommand) interceptor.getParamsMap().get("command")).getIds().getFirst();
                yield casesRepository.selectById(qaId).getRepoId();
            }
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return kbRepoBaseInfoMapper.selectById(kbId).getTeamId();
    }
}
