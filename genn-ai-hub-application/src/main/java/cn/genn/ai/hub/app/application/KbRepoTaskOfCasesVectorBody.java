package cn.genn.ai.hub.app.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * qa问答对向量化任务请求体
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoTaskOfCasesVectorBody implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long tenantId;

    private Long repoId;

    private Long casesId;

    private String vectorModelKey;

}

