package cn.genn.ai.hub.app.infrastructure.utils.vector.milvus;

import cn.genn.ai.hub.app.application.dto.KbRepoCasesDTO;
import cn.genn.ai.hub.app.application.dto.embedding.CasesEmbeddingDataDTO;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.vector.request.DeleteReq;
import io.milvus.v2.service.vector.request.InsertReq;
import jakarta.annotation.Resource;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 问答对向量库操作
 */
@Component
public class CasesVectorUtil {

    @Resource
    private MilvusClientV2 milvusClient;
    @Resource
    private AIModelManager aiModelManager;
    @Resource
    private GennAIHubProperties properties;

    public void createOrUpdateCasesVector(String embeddingModelKey, KbRepoCasesDTO casesDTO) {
        if (Objects.isNull(casesDTO)) {
            return;
        }

        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfCases(embeddingModelKey))
            .filter("cases_id==" + casesDTO.getId())
            .build();
        milvusClient.delete(deleteReq);

        EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(embeddingModelKey);
        float[] embed = embeddingModel.embed(casesDTO.getTitle());
        List<Float> embedding = new ArrayList<>(embed.length);
        for (Float f : embed) {
            embedding.add(f);
        }
        CasesEmbeddingDataDTO data = CasesEmbeddingDataDTO.builder()
            .tenantId(casesDTO.getTenantId())
            .repoId(casesDTO.getRepoId())
            .casesId(casesDTO.getId())
            .title(casesDTO.getTitle())
            .content(casesDTO.getContent())
            .impact(casesDTO.getImpact())
            .embedding(embedding)
            .build();
        Gson gson = new Gson();
        JsonObject jsonObject = gson.toJsonTree(data).getAsJsonObject();
        milvusClient.insert(InsertReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfCases(embeddingModelKey))
            .data(Collections.singletonList(jsonObject))
            .build());
    }

    public void deleteCasesVectorByCasesId(String vectorModelKey, Long casesId) {
        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfCases(vectorModelKey))
            .filter("cases_id==" + casesId)
            .build();
        milvusClient.delete(deleteReq);
    }

    public void deleteCasesVectorByCasesIds(String vectorModelKey, List<Long> casesIds) {
        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfCases(vectorModelKey))
            // 转换为类似 ['key1', 'key2', 'key3'] 的格式
            .filter("cases_id in " + casesIds
                .stream()
                .map(key -> Long.toString(key))
                .collect(Collectors.joining(", ", "[", "]")))
            .build();
        milvusClient.delete(deleteReq);
    }

}
