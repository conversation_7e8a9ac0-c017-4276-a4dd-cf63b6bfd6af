package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.KbRepoTaskOfCasesVectorBody;
import cn.genn.ai.hub.app.application.dto.KbRepoCasesDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.CasesRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.utils.vector.milvus.CasesVectorUtil;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static cn.genn.ai.hub.app.application.enums.TaskTypeEnum.CASES_VECTOR;

/**
 * 案例 向量化任务处理器
 */
@Component
@Slf4j
public class CasesVectorTaskHandler extends AbstractTaskHandler {

    @Resource
    private CasesRepositoryImpl repositoryImpl;

    @Resource
    private RepoTaskRepositoryImpl taskRepository;

    @Resource
    private CasesVectorUtil casesVectorUtil;

    @Override
    public TaskTypeEnum getType() {
        return CASES_VECTOR;
    }


    @Override
    public void invoke(KbRepoTaskDTO task) {
        // 异步调用算法接口的实现
        // 接受算法的异步回调结果 更新任务状态 更新库里索引的算法处理状态
        async(() -> {
            KbRepoTaskOfCasesVectorBody body = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfCasesVectorBody.class);
            KbRepoCasesDTO casesDTO = repositoryImpl.selectById(body.getCasesId());
            if (Objects.isNull(casesDTO)) {
                // 更新任务为完成
                taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
                return;
            }
            repositoryImpl.updateCasesHandleStatus(body.getCasesId(), HandleStatusEnum.PROCESSING);
            casesVectorUtil.createOrUpdateCasesVector(body.getVectorModelKey(), casesDTO);

            // 更新案例为算法处理完成
            repositoryImpl.updateCasesHandleStatus(body.getCasesId(), HandleStatusEnum.DONE);
            // 更新任务为完成
            taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
            // 移除进行队列任务
            SpringUtil.getBean(TaskHandlerFactory.class).removeProcessTask(task);
        });
    }

}
