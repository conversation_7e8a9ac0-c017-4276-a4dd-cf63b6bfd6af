package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.rtc.RTCPitfallItemSubmitCommand;
import cn.genn.ai.hub.app.application.command.rtc.RTCPitfallSubmitCommand;
import cn.genn.ai.hub.app.application.dto.rtc.*;
import cn.genn.ai.hub.app.application.query.rtc.RTCPitfallInfoQuery;
import cn.genn.ai.hub.app.domain.rtc.model.aggregates.RTCPitfallAgg;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallInfo;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallItem;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallAnalysisPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallInfoPO;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.core.model.enums.DeletedEnum;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RTCPitfallAssembler extends QueryAssembler<RTCPitfallInfoQuery, RTCPitfallInfoPO, RTCPitfallInfoDTO> {

    RTCPitfallAssembler INSTANCE = Mappers.getMapper(RTCPitfallAssembler.class);

    RTCPitfallInfo toRTCPitfallInfo(RTCPitfallSubmitCommand command);

    RTCPitfallInfoDTO toRTCPitfallInfoDTO(RTCPitfallInfo info);

    RTCPitfallDetailDTO toRTCPitfallDetailDTO(RTCPitfallInfo rtcPitfallInfo);

    List<RTCPitfallItem> toRTCPitfallItem(List<RTCPitfallItemSubmitCommand> pitfallItems);

    List<RTCPitfallItemDTO> info2RTCPitfallItem(List<RTCPitfallItem> rtcPitfallItem);

    RTCPitfallAnalysisDTO toRTCPitfallAnalysisDTO(RTCPitfallAnalysisPO po);

    default RTCPitfallAgg toRTCPitfallAgg(RTCPitfallSubmitCommand command){
        RTCPitfallInfo rtcPitfallInfo = toRTCPitfallInfo(command);
        return RTCPitfallAgg.builder().rtcPitfallInfo(rtcPitfallInfo)
            .rtcPitfallItems(toRTCPitfallItem(command.getPitfallItems()))
                .build();
    }

    default void fillRTCPitfallAgg(RTCPitfallAgg rtcPitfallAgg, RTCPitfallSubmitCommand command){
        fillRTCPitfallInfo(rtcPitfallAgg.getRtcPitfallInfo(), command);
        rtcPitfallAgg.setRtcPitfallItems(toRTCPitfallItem(command.getPitfallItems()));
    }

    default void fillRTCPitfallInfo(RTCPitfallInfo info, RTCPitfallSubmitCommand command){
        info.setPitfallArea(command.getPitfallArea());
        info.setAppId(command.getAppId());
        info.setUploadContent(command.getUploadContent());
        info.setStatus(command.getStatus());
    }

    default void fillSubmitRTCPitfallItem(List<RTCPitfallItem> items, List<RTCPitfallItemSubmitCommand> pitfallItem){
        Map<Long, RTCPitfallItemSubmitCommand> map = pitfallItem.stream().collect(Collectors.toMap(RTCPitfallItemSubmitCommand::getId, Function.identity(), (item1, item2) -> item1));
        items.forEach(item -> {
            if (map.containsKey(item.getId())){
                RTCPitfallItemSubmitCommand command = map.get(item.getId());
                item.setPitfallType(command.getPitfallType());
                item.setPitfallContent(command.getPitfallContent());
            }else {
                item.setDeleted(DeletedEnum.DELETED);
            }
        });
    }

    default void fillConfirmRTCPitfallItem(List<RTCPitfallItem> items, List<RTCPitfallItemSubmitCommand> pitfallItem){
        Map<Long, RTCPitfallItemSubmitCommand> map = pitfallItem.stream().collect(Collectors.toMap(RTCPitfallItemSubmitCommand::getId, Function.identity(), (item1, item2) -> item1));
        items.forEach(item -> {
            if (map.containsKey(item.getId())){
                RTCPitfallItemSubmitCommand command = map.get(item.getId());
                item.setConfirmStatus(command.getConfirmStatus());
                item.setPitfallContent(command.getPitfallContent());
            }
        });
    }

    default void fillResolveRTCPitfallItem(List<RTCPitfallItem> items, List<RTCPitfallItemSubmitCommand> pitfallItem){
        Map<Long, RTCPitfallItemSubmitCommand> map = pitfallItem.stream().collect(Collectors.toMap(RTCPitfallItemSubmitCommand::getId, Function.identity(), (item1, item2) -> item1));
        items.forEach(item -> {
            if (map.containsKey(item.getId())){
                RTCPitfallItemSubmitCommand command = map.get(item.getId());
                item.setPitfallContent(command.getPitfallContent());
            }
        });
    }

    default List<RTCPitfallDTO> toRTCPitfallDTO(List<RTCPitfallPageDTO> rtcPitfallPageDTOs){
        return Optional.ofNullable(rtcPitfallPageDTOs).orElse(Collections.emptyList()).stream().map(rtcPitfallPageDTO -> RTCPitfallDTO.builder()
            .pitfallArea(rtcPitfallPageDTO.getPitfallArea())
            .description(rtcPitfallPageDTO.getPitfallContent().getDescription())
            .pitfallType(rtcPitfallPageDTO.getPitfallType().getDescription())
            .checkBasis(rtcPitfallPageDTO.getPitfallContent().getCheckBasis())
            .measure(rtcPitfallPageDTO.getPitfallContent().getMeasure())
            .solution(rtcPitfallPageDTO.getPitfallContent().getSolution()).build()).collect(Collectors.toList());
    }
}

