package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.KbRepoTaskOfMemoryEventsVectorBody;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.dto.MemoryEventsDTO;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.MemoryEventsRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.utils.vector.milvus.MemoryEventsVectorUtil;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static cn.genn.ai.hub.app.application.enums.TaskTypeEnum.MEMORY_EVENTS_VECTOR;

/**
 * 情景记忆事件 向量化任务处理器
 */
@Component
@Slf4j
public class MemoryEventsVectorTaskHandler extends AbstractTaskHandler {

    @Resource
    private MemoryEventsRepositoryImpl repositoryImpl;

    @Resource
    private RepoTaskRepositoryImpl taskRepository;

    @Resource
    private MemoryEventsVectorUtil memoryEventsVectorUtil;

    @Override
    public TaskTypeEnum getType() {
        return MEMORY_EVENTS_VECTOR;
    }

    @Override
    public void invoke(KbRepoTaskDTO task) {
        // 异步调用算法接口的实现
        // 接受算法的异步回调结果 更新任务状态 更新库里索引的算法处理状态
        async(() -> {
            KbRepoTaskOfMemoryEventsVectorBody body = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfMemoryEventsVectorBody.class);
            MemoryEventsDTO memoryEventsDTO = repositoryImpl.selectById(body.getMemoryEventsId());
            if (Objects.isNull(memoryEventsDTO)) {
                // 更新任务为完成
                taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
                return;
            }
            repositoryImpl.updateMemoryEventsHandleStatus(body.getMemoryEventsId(), HandleStatusEnum.PROCESSING);
            memoryEventsVectorUtil.createOrUpdateMemoryEventsVector(body.getVectorModelKey(), memoryEventsDTO);

            // 更新情景记忆事件为算法处理完成
            repositoryImpl.updateMemoryEventsHandleStatus(body.getMemoryEventsId(), HandleStatusEnum.DONE);
            // 更新任务为完成
            taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
            // 移除进行队列任务
            SpringUtil.getBean(TaskHandlerFactory.class).removeProcessTask(task);
        });
    }

}