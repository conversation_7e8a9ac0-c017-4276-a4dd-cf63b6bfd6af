package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.MemoryEventsCommand;
import cn.genn.ai.hub.app.application.dto.MemoryEventsDTO;
import cn.genn.ai.hub.app.application.dto.MemoryEventsExcelImportDTO;
import cn.genn.ai.hub.app.application.query.MemoryEventsQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.MemoryEventsPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MemoryEventsAssembler extends QueryAssembler<MemoryEventsQuery, MemoryEventsPO, MemoryEventsDTO> {

    MemoryEventsAssembler INSTANCE = Mappers.getMapper(MemoryEventsAssembler.class);

    MemoryEventsPO convertPO(MemoryEventsCommand command);

    List<MemoryEventsPO> convertPOs(List<MemoryEventsCommand> commands);

    List<MemoryEventsCommand> convertCommand(List<MemoryEventsExcelImportDTO> dataList);

}