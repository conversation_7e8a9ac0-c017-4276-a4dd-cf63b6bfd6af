package cn.genn.ai.hub.app.application.listener.govow;

import cn.genn.ai.hub.app.application.enums.DailyAnalysisEnum;
import cn.genn.ai.hub.app.application.service.govow.GovowOperationService;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.hutool.core.text.CharSequenceUtil;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GovowFsRecommendPushJob extends AbstractJobHandler {

    private final GovowOperationService govowOperationService;

    @Override
    public void doExecute() {
        String jobParam = XxlJobHelper.getJobParam();
        if (CharSequenceUtil.isEmpty(jobParam)) {
            XxlJobHelper.log("GovowFsRecommendPushJob jobParam is empty");
            return;
        }
        GovowFsQuestionPushParam pushParam = JsonUtils.parse(jobParam, GovowFsQuestionPushParam.class);
        if (pushParam == null) {
            XxlJobHelper.log("GovowFsRecommendPushJob pushParam is null");
            return;
        }
        govowOperationService.pushQuestionToFeishu(pushParam, DailyAnalysisEnum.RECOMMEND);
    }
}
