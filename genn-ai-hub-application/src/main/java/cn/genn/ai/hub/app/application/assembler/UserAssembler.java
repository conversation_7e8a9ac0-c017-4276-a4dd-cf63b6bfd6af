package cn.genn.ai.hub.app.application.assembler;

import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.BeanUtils;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserAssembler {

    default SsoUserAuthInfoDTO userDTO2AuthInfoDTO(UpmUserDTO upmUserDTO) {
        SsoUserAuthInfoDTO result = new SsoUserAuthInfoDTO();
        BeanUtils.copyProperties(upmUserDTO, result);
        result.setUserId(upmUserDTO.getId());
        if(upmUserDTO.getFsUser() != null){
            result.setFsOpenId(upmUserDTO.getFsUser().getFsOpenId());
        }
        return result;
    }
}
