package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.KbRepoCasesCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoCasesDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoCasesExcelImportDTO;
import cn.genn.ai.hub.app.application.query.KbRepoCasesQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCasesPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoCasesAssembler extends QueryAssembler<KbRepoCasesQuery, KbRepoCasesPO, KbRepoCasesDTO> {

    KbRepoCasesAssembler INSTANCE = Mappers.getMapper(KbRepoCasesAssembler.class);

    KbRepoCasesPO convertPO(KbRepoCasesCommand command);

    List<KbRepoCasesPO> convertPOs(List<KbRepoCasesCommand> commands);

    List<KbRepoCasesCommand> convertCommand(List<KbRepoCasesExcelImportDTO> dataList);
}

