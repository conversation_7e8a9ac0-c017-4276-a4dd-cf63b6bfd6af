package cn.genn.ai.hub.app.infrastructure.repository.po;

import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * MemoryEventsPO对象
 *
 * <AUTHOR>
 * @desc 情景记忆事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName(value = "memory_events", autoResultMap = true)
public class MemoryEventsPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 主体
     */
    @TableField("subject")
    private String subject;

    /**
     * 时间
     */
    @TableField("time")
    private String time;

    /**
     * 事件
     */
    @TableField("event")
    private String event;

    /**
     * 原因
     */
    @TableField("cause")
    private String cause;

    /**
     * 如何处理
     */
    @TableField("process")
    private String process;

    /**
     * 处理状态,0:WAIT-未处理, 1:PROCESSING-处理中,2:DONE-已处理
     */
    @TableField("handle_status")
    private HandleStatusEnum handleStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

}