package cn.genn.ai.hub.app.application.service.govow;

import cn.genn.ai.cerebro.model.CerebroAuth;
import cn.genn.ai.cerebro.model.CerebroMessage;
import cn.genn.ai.cerebro.model.CerebroRequest;
import cn.genn.ai.cerebro.model.CerebroResponse;
import cn.genn.ai.cerebro.service.CerebroService;
import cn.genn.ai.hub.app.application.assembler.GovowJobAssembler;
import cn.genn.ai.hub.app.application.assembler.UserAssembler;
import cn.genn.ai.hub.app.application.client.AgentChatClient;
import cn.genn.ai.hub.app.application.command.JobSaveOrUpdateCommand;
import cn.genn.ai.hub.app.application.command.govow.ExecutionRequest;
import cn.genn.ai.hub.app.application.command.govow.JobCompletionsCommand;
import cn.genn.ai.hub.app.application.command.govow.JobEvaluateCommand;
import cn.genn.ai.hub.app.application.dto.govow.AgentMessage;
import cn.genn.ai.hub.app.application.dto.govow.JobTasksDTO;
import cn.genn.ai.hub.app.application.enums.ChatSourceType;
import cn.genn.ai.hub.app.application.enums.ExecutionMode;
import cn.genn.ai.hub.app.application.query.govow.JobQuery;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.model.task.JobScheduledTasksDTO;
import cn.genn.job.xxl.model.task.TaskQuery;
import cn.genn.job.xxl.model.task.TaskSaveOrUpdateCommand;
import cn.genn.job.xxl.model.task.UpdateStatusCommand;
import cn.genn.job.xxl.service.ScheduledTaskService;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class GovowJobService {

    private final ScheduledTaskService scheduledTaskService;
    private final GovowJobAssembler govowJobAssembler;
    private final CerebroService cerebroService;
    private final GennAIHubProperties gennAIHubProperties;
    private final AgentChatClient agentChatClient;
    private final IUpmUserService upmUserService;
    private final UserAssembler userAssembler;

    private final static String BIZ_KEY_PRE = "govow_job_";

    public void saveOrUpdate(JobSaveOrUpdateCommand command) {
        String bizKey = BIZ_KEY_PRE + CurrentUserHolder.getUserId();
        TaskSaveOrUpdateCommand taskCommand = govowJobAssembler.jobCommand2TaskCommand(command, bizKey);
        scheduledTaskService.saveOrUpdate(taskCommand);
    }

    public void delete(IdCommand command) {
        scheduledTaskService.delete(command);
    }

    public void updateStatus(UpdateStatusCommand command) {
        scheduledTaskService.updateStatus(command);
    }

    public List<JobTasksDTO> query(JobQuery query) {
        TaskQuery taskQuery = new TaskQuery();
        String bizKey = BIZ_KEY_PRE + CurrentUserHolder.getUserId();
        taskQuery.setBizKey(bizKey);
        List<JobScheduledTasksDTO> scheduledTasksDTOS = scheduledTaskService.query(taskQuery);
        return govowJobAssembler.task2Job(scheduledTasksDTOS);
    }

    public Flux<CerebroResponse> evaluate(JobEvaluateCommand command, String cookie) {
        CerebroMessage message = new CerebroMessage().setContent(command.getContent());
        Map<String, String> variables = new HashMap<>();
        variables.put("question",command.getContent());
        CerebroRequest request = new CerebroRequest()
                .setAppId(gennAIHubProperties.getJob().getAppId())
            .setMessages(Collections.singletonList(message))
                .setVariables(variables);
        CerebroAuth auth = new CerebroAuth()
                .setType(CerebroAuth.AuthType.COOKIE)
                .setCookie(cookie);
        return cerebroService.executeStream(request, auth);
    }
    public String completions(JobCompletionsCommand command) {
        //userId获取用户信息
        UpmUserQuery query = new UpmUserQuery();
        query.setUserIdList(Collections.singletonList(command.getUserId()));
        query.setDeleted(DeletedEnum.NOT_DELETED);
        List<UpmUserDTO> upmUserDTOS = upmUserService.conditionList(query);
        if(CollUtil.isEmpty(upmUserDTOS)){
            return "未找到用户,userId:"+command.getUserId();
        }
        LocalDateTime effectiveTime = upmUserDTOS.getFirst().getEffectiveTime();
        if(ObjUtil.isNotNull(effectiveTime) && effectiveTime.isBefore(LocalDateTime.now())){
            return "用户已过期,跳过执行";
        }
        Map<String, Object> parameters = Maps.newHashMap();
        if(CollUtil.isNotEmpty(command.getSupplements())){
            parameters.put("supplements", JsonUtils.toJson(command.getSupplements()));
        }
        parameters.put("source",ChatSourceType.SCHEDULED_TASK.getCode());
        parameters.put("questionShort",command.getTaskName());

        SsoUserAuthInfoDTO authInfoDTO = userAssembler.userDTO2AuthInfoDTO(upmUserDTOS.getFirst());
        ExecutionRequest request = new ExecutionRequest();
        request.setMode(ExecutionMode.WORKFLOW);
        request.setChatMode(command.getChatMode());
        request.setInput(command.getContent());
        request.setFileInfo(Lists.newArrayList());
        request.setChatId(IdUtil.fastSimpleUUID());
        request.setAppId("default");
        request.setParameters(parameters);
        request.setStream(false);
        request.setUserInfo(authInfoDTO);
        request.setHttpHeaders(Maps.newHashMap());
        request.setNewTask(true);
        request.setSource(ChatSourceType.SCHEDULED_TASK);

        String magicToken = URLEncodeUtil.encode(JsonUtils.toJsonNotNull(authInfoDTO));
        CerebroAuth auth = new CerebroAuth()
                .setType(CerebroAuth.AuthType.MAGIC_TOKEN)
                .setMagicTokenData(magicToken);
        agentChatClient.chat(request, auth).subscribe();
        return "执行成功!";
    }

    /**
     * 为反射调用提供的重载方法，接收Map参数
     * LocalMethodTaskExecutor会调用这个方法
     */
    public String completions(Map<String, Object> params) {
        // 从Map中提取参数并构建JobCompletionsCommand
        JobCompletionsCommand command = new JobCompletionsCommand();
        command.setUserId((String) params.get("userId"));
        command.setChatMode(ChatMode.fromCode((String) params.get("chatMode")));
        command.setContent((String) params.get("content"));
        command.setTaskName((String) params.get("taskName"));

        // 处理supplements参数
        Object supplementsObj = params.get("supplements");
        if (supplementsObj != null && supplementsObj instanceof String) {
            try {
                List<ContentSupplement> supplements = JsonUtils.parseToList((String) supplementsObj, ContentSupplement.class);
                command.setSupplements(supplements);
            } catch (Exception e) {
                log.warn("Failed to parse supplements: {}", e.getMessage());
            }
        }

        // 调用原有的completions方法
        return completions(command);
    }


}
