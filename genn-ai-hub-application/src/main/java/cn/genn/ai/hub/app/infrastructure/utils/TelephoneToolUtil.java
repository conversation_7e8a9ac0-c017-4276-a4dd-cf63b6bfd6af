package cn.genn.ai.hub.app.infrastructure.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TelephoneToolUtil {

    /**
     * 文本中尝试取出11位的手机号
     * @param text
     * @return
     */
    public static String extractPhoneNumber(String text) {
        if (text == null || text.isEmpty()) {
            return null;
        }

        // 匹配11位数字的正则表达式
        Pattern pattern = Pattern.compile("1[3-9]\\d{9}");
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            return matcher.group();
        }

        return null;
    }
}
