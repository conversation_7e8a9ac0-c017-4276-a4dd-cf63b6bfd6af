package cn.genn.ai.hub.app.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 情景记忆事件向量化任务体
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoTaskOfMemoryEventsVectorBody {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 记忆事件ID
     */
    private Long memoryEventsId;

    /**
     * 向量模型键
     */
    private String vectorModelKey;

}