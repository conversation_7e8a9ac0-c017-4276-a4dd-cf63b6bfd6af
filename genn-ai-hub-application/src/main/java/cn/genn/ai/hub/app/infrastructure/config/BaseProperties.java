package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class BaseProperties {

    private Long systemId = 16L;

    /**
     * 用户默认密码
     */
    private String defaultPassword = "GenN@2651#";

    /**
     * 默认权限组
     */
    private String defaultAuthKey = "pl-16-1";

    /**
     * 默认角色ids
     */
    private List<Long> defaultRoleIds = new ArrayList<>(Arrays.asList(1033L));
}
